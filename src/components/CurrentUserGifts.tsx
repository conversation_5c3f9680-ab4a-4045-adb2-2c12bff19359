"use client";

import { useState } from "react";
import { Player } from "@lottiefiles/react-lottie-player";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useUserGifts, UserStarGift } from "@/hooks/useUserGifts";
import { Gift, Star, Calendar, User, RefreshCw, Loader2 } from "lucide-react";

interface GiftTileProps {
  gift: UserStarGift;
  onClick?: () => void;
}

const GiftTile = ({ gift, onClick }: GiftTileProps) => {
  const [imageError, setImageError] = useState(false);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const formatStars = (stars: number) => {
    return stars.toLocaleString();
  };

  return (
    <Card
      className="group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105 bg-gradient-to-br from-slate-800 to-slate-900 border-slate-700 text-white"
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-white truncate">
            {gift.name}
          </CardTitle>
          <Badge
            variant="secondary"
            className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
          >
            <Star className="w-3 h-3 mr-1" />
            {formatStars(gift.stars)}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Lottie Animation / Sticker Display */}
        <div className="flex justify-center items-center h-32 bg-slate-700/50 rounded-lg">
          {gift.sticker.is_animated && !imageError ? (
            <div className="relative">
              <Player
                autoplay
                loop
                src={`https://api.telegram.org/file/bot${process.env.NEXT_PUBLIC_BOT_TOKEN}/${gift.sticker.file_id}`}
                style={{ height: "80px", width: "80px" }}
                onEvent={(event) => {
                  if (event === "error") {
                    setImageError(true);
                  }
                }}
              />
              {/* Fallback emoji overlay in case lottie fails to load */}
              {imageError && (
                <div className="absolute inset-0 flex items-center justify-center text-4xl">
                  {gift.sticker.emoji ?? "🎁"}
                </div>
              )}
            </div>
          ) : (
            <div className="text-4xl animate-pulse">
              {gift.sticker.emoji ?? "🎁"}
            </div>
          )}
        </div>

        {/* Gift Details */}
        <div className="space-y-2">
          <div className="flex items-center text-sm text-gray-300">
            <Calendar className="w-4 h-4 mr-2" />
            {formatDate(gift.date)}
          </div>

          {gift.from_id && !gift.name_hidden && (
            <div className="flex items-center text-sm text-gray-300">
              <User className="w-4 h-4 mr-2" />
              From: {gift.from_id}
            </div>
          )}

          {gift.name_hidden && (
            <div className="flex items-center text-sm text-gray-400">
              <User className="w-4 h-4 mr-2" />
              Anonymous sender
            </div>
          )}

          {gift.message && (
            <div className="text-sm text-gray-300 italic truncate">
              "{gift.message}"
            </div>
          )}

          {gift.convert_stars && (
            <div className="flex items-center text-sm text-green-400">
              <Star className="w-4 h-4 mr-2" />
              Convert: {formatStars(gift.convert_stars)} stars
            </div>
          )}
        </div>

        {/* Status Badges */}
        <div className="flex flex-wrap gap-1">
          {gift.unsaved && (
            <Badge
              variant="outline"
              className="text-xs border-orange-500/30 text-orange-400"
            >
              Unsaved
            </Badge>
          )}
          {gift.sticker.is_animated && (
            <Badge
              variant="outline"
              className="text-xs border-blue-500/30 text-blue-400"
            >
              Animated
            </Badge>
          )}
          {gift.sticker.is_video && (
            <Badge
              variant="outline"
              className="text-xs border-purple-500/30 text-purple-400"
            >
              Video
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

interface CurrentUserGiftsProps {
  className?: string;
}

export const CurrentUserGifts = ({ className }: CurrentUserGiftsProps) => {
  const { gifts, loading, error, hasMore, loadMore, refresh, totalCount } =
    useUserGifts();

  const handleGiftClick = (gift: UserStarGift) => {
    // Here you could open a modal or navigate to a detail page
    console.log("Selected gift:", gift);
  };

  const handleRefresh = () => {
    refresh();
  };

  if (error) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-400 mb-4">
            <Gift className="w-12 h-12 mx-auto mb-2" />
            <p className="text-lg font-semibold">Error loading gifts</p>
            <p className="text-sm text-gray-400">{error}</p>
          </div>
          <Button onClick={handleRefresh} variant="outline" className="mt-4">
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center">
            <Gift className="w-6 h-6 mr-2" />
            My Star Gifts
          </h2>
          <p className="text-gray-400 text-sm">
            {totalCount > 0 ? `${totalCount} gifts received` : "No gifts yet"}
          </p>
        </div>
        <Button
          onClick={handleRefresh}
          variant="outline"
          size="sm"
          disabled={loading}
          className="border-slate-600 text-gray-300 hover:bg-slate-700"
        >
          <RefreshCw
            className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
          />
          Refresh
        </Button>
      </div>

      {/* Loading State */}
      {loading && gifts.length === 0 && (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-400">Loading your gifts...</span>
        </div>
      )}

      {/* Empty State */}
      {!loading && gifts.length === 0 && !error && (
        <div className="text-center py-12">
          <Gift className="w-16 h-16 mx-auto mb-4 text-gray-500" />
          <h3 className="text-xl font-semibold text-gray-300 mb-2">
            No gifts yet
          </h3>
          <p className="text-gray-400">
            Star gifts you receive will appear here
          </p>
        </div>
      )}

      {/* Gifts Grid */}
      {gifts.length > 0 && (
        <>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6">
            {gifts.map((gift) => (
              <GiftTile
                key={gift.id}
                gift={gift}
                onClick={() => handleGiftClick(gift)}
              />
            ))}
          </div>

          {/* Load More Button */}
          {hasMore && (
            <div className="text-center">
              <Button
                onClick={loadMore}
                variant="outline"
                disabled={loading}
                className="border-slate-600 text-gray-300 hover:bg-slate-700"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Loading...
                  </>
                ) : (
                  "Load More"
                )}
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
};
