import { NextRequest, NextResponse } from "next/server";
import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions";
import bigInt from "big-integer";

interface UserStarGift {
  name_hidden?: boolean;
  unsaved?: boolean;
  from_id?: number;
  date: number;
  gift: {
    limited?: boolean;
    sold_out?: boolean;
    birthday?: boolean;
    id: number;
    sticker: {
      file_id: string;
      file_unique_id: string;
      width: number;
      height: number;
      is_animated: boolean;
      is_video: boolean;
      thumb?: {
        file_id: string;
        file_unique_id: string;
        width: number;
        height: number;
        file_size?: number;
      };
      emoji?: string;
      set_name?: string;
      file_size?: number;
    };
    stars: number;
    availability_remains?: number;
    availability_total?: number;
    convert_stars: number;
    first_sale_date?: number;
    last_sale_date?: number;
  };
  message?: {
    text: string;
    entities?: any[];
  };
  msg_id?: number;
  convert_stars?: number;
}

interface UserStarGiftsResponse {
  count: number;
  gifts: UserStarGift[];
  next_offset?: string;
  users: any[];
}

// Validate Telegram initData (basic validation)
function validateInitData(initData: string): boolean {
  try {
    // Basic validation - check if it contains required fields
    return initData.includes("user=") && initData.includes("auth_date=");
  } catch {
    return false;
  }
}

// Create and configure Telegram client
async function createTelegramClient(): Promise<TelegramClient> {
  const apiId = parseInt(process.env.TELEGRAM_API_ID!);
  const apiHash = process.env.TELEGRAM_API_HASH!;

  if (!apiId || !apiHash) {
    throw new Error(
      "TELEGRAM_API_ID and TELEGRAM_API_HASH must be set in environment variables"
    );
  }

  const client = new TelegramClient(
    new StringSession(""), // Empty session for bot-like usage
    apiId,
    apiHash,
    {
      connectionRetries: 5,
      useWSS: false,
    }
  );

  return client;
}

// Transform Telegram API response to our interface
function transformTelegramGift(userStarGift: any): UserStarGift {
  return {
    name_hidden: userStarGift.nameHidden || false,
    unsaved: userStarGift.unsaved || false,
    from_id: userStarGift.fromId?.value || undefined,
    date: userStarGift.date || Math.floor(Date.now() / 1000),
    gift: {
      limited: userStarGift.gift?.limited || false,
      sold_out: userStarGift.gift?.soldOut || false,
      birthday: userStarGift.gift?.birthday || false,
      id: userStarGift.gift?.id?.value || 0,
      sticker: {
        file_id: userStarGift.gift?.sticker?.id?.toString() || "",
        file_unique_id:
          userStarGift.gift?.sticker?.fileReference?.toString() || "",
        width: userStarGift.gift?.sticker?.w || 512,
        height: userStarGift.gift?.sticker?.h || 512,
        is_animated: userStarGift.gift?.sticker?.animated || false,
        is_video: userStarGift.gift?.sticker?.videoSticker || false,
        emoji: userStarGift.gift?.sticker?.emoji || "⭐",
        set_name: userStarGift.gift?.sticker?.setName || "star_gifts",
      },
      stars: userStarGift.gift?.stars?.value || 0,
      availability_remains: userStarGift.gift?.availabilityRemains || undefined,
      availability_total: userStarGift.gift?.availabilityTotal || undefined,
      convert_stars: userStarGift.gift?.convertStars?.value || 0,
      first_sale_date: userStarGift.gift?.firstSaleDate || undefined,
      last_sale_date: userStarGift.gift?.lastSaleDate || undefined,
    },
    message: userStarGift.message
      ? {
          text: userStarGift.message.text || "",
          entities: userStarGift.message.entities || [],
        }
      : undefined,
    msg_id: userStarGift.msgId || undefined,
    convert_stars: userStarGift.convertStars?.value || undefined,
  };
}

// Get user star gifts using real Telegram API
async function getUserStarGifts(
  userId: number,
  offset: string = "",
  limit: number = 20
): Promise<UserStarGiftsResponse> {
  let client: TelegramClient | null = null;

  try {
    client = await createTelegramClient();

    // Start the client with bot token authentication
    await client.start({
      botAuthToken: process.env.TELEGRAM_BOT_TOKEN!,
    });

    // Create InputUser for the API call
    const inputUser = new Api.InputUser({
      userId: bigInt(userId),
      accessHash: bigInt(0), // For public users, access hash can be 0
    });

    // Call the getUserStarGifts API (correct method name from documentation)
    const result = await client.invoke(
      new Api.payments.GetUserStarGifts({
        userId: inputUser,
        offset: offset,
        limit: limit,
      })
    );

    // Transform the response
    const transformedGifts = result.gifts?.map(transformTelegramGift) ?? [];

    return {
      count: result.count ?? 0,
      gifts: transformedGifts,
      next_offset: result.nextOffset ?? undefined,
      users: result.users ?? [],
    };
  } catch (error) {
    console.error("Error fetching user star gifts from Telegram API:", error);

    // Fallback to empty response on error
    return {
      count: 0,
      gifts: [],
      next_offset: undefined,
      users: [],
    };
  } finally {
    // Always disconnect the client
    if (client) {
      try {
        await client.disconnect();
      } catch (disconnectError) {
        console.error("Error disconnecting Telegram client:", disconnectError);
      }
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { user_id, offset = "", limit = 20, initData } = body;

    // Validate required parameters
    if (!user_id) {
      return NextResponse.json(
        { error: "user_id is required" },
        { status: 400 }
      );
    }

    if (!initData) {
      return NextResponse.json(
        { error: "initData is required for authentication" },
        { status: 400 }
      );
    }

    // Validate initData format
    if (!validateInitData(initData)) {
      return NextResponse.json(
        { error: "Invalid initData format" },
        { status: 400 }
      );
    }

    // Get user star gifts using real Telegram API
    const response = await getUserStarGifts(user_id, offset, limit);

    return NextResponse.json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("Error fetching user gifts:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch user gifts",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: "Method not allowed. Use POST instead." },
    { status: 405 }
  );
}
