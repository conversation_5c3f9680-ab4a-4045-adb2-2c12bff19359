"use client";

import Link from "next/link";

export default function Home() {
  return (
    <div className="container mx-auto py-8">
      <div className="max-w-4xl mx-auto text-center">
        <h1 className="text-4xl font-bold mb-8">Marketplace UI</h1>
        <p className="text-lg text-muted-foreground mb-8">
          A marketplace application with Firebase authentication, admin panel,
          and TON wallet integration.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <Link
            href="/marketplace"
            className="block p-6 border rounded-lg hover:bg-muted/50 transition-colors bg-gradient-to-r from-blue-50 to-cyan-50"
          >
            <h2 className="text-xl font-semibold mb-2">Telegram Auth & Test</h2>
            <p className="text-muted-foreground">
              Telegram Web App authentication, debugging, and testing
            </p>
          </Link>

          <Link
            href="/auth"
            className="block p-6 border rounded-lg hover:bg-muted/50 transition-colors"
          >
            <h2 className="text-xl font-semibold mb-2">Admin Auth</h2>
            <p className="text-muted-foreground">
              Firebase authentication with Google sign-in for admins
            </p>
          </Link>

          <Link
            href="/admin"
            className="block p-6 border rounded-lg hover:bg-muted/50 transition-colors"
          >
            <h2 className="text-xl font-semibold mb-2">Admin Panel</h2>
            <p className="text-muted-foreground">
              Manage collections with CRUD operations
            </p>
          </Link>

          <Link
            href="/profile"
            className="block p-6 border rounded-lg hover:bg-muted/50 transition-colors"
          >
            <h2 className="text-xl font-semibold mb-2">Profile</h2>
            <p className="text-muted-foreground">
              Update your personal information and wallet details
            </p>
          </Link>

          <Link
            href="/ton-connect"
            className="block p-6 border rounded-lg hover:bg-muted/50 transition-colors bg-gradient-to-r from-purple-50 to-pink-50"
          >
            <h2 className="text-xl font-semibold mb-2">TON Connect</h2>
            <p className="text-muted-foreground">
              Connect TON wallet and view detailed information
            </p>
          </Link>
        </div>
      </div>
    </div>
  );
}
