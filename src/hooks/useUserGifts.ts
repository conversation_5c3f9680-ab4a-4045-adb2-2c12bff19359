"use client";

import { useState, useEffect, useCallback } from "react";
import { initData, useSignal } from "@telegram-apps/sdk-react";

export interface UserStarGift {
  id: string;
  name: string;
  sticker: {
    file_id: string;
    file_unique_id: string;
    width: number;
    height: number;
    is_animated: boolean;
    is_video: boolean;
    thumb?: {
      file_id: string;
      file_unique_id: string;
      width: number;
      height: number;
      file_size?: number;
    };
    emoji?: string;
    set_name?: string;
    file_size?: number;
  };
  stars: number;
  date: number;
  from_id?: number;
  message?: string;
  name_hidden?: boolean;
  unsaved?: boolean;
  convert_stars?: number;
}

// API Response types (from Telegram API)
export interface TelegramUserStarGift {
  name_hidden?: boolean;
  unsaved?: boolean;
  from_id?: number;
  date: number;
  gift: {
    limited?: boolean;
    sold_out?: boolean;
    birthday?: boolean;
    id: number;
    sticker: {
      file_id: string;
      file_unique_id: string;
      width: number;
      height: number;
      is_animated: boolean;
      is_video: boolean;
      thumb?: {
        file_id: string;
        file_unique_id: string;
        width: number;
        height: number;
        file_size?: number;
      };
      emoji?: string;
      set_name?: string;
      file_size?: number;
    };
    stars: number;
    availability_remains?: number;
    availability_total?: number;
    convert_stars: number;
    first_sale_date?: number;
    last_sale_date?: number;
  };
  message?: {
    text: string;
    entities?: any[];
  };
  msg_id?: number;
  convert_stars?: number;
}

export interface UserGiftsResponse {
  count: number;
  gifts: TelegramUserStarGift[];
  next_offset?: string;
  users: any[];
}

interface UseUserGiftsOptions {
  limit?: number;
  offset?: string;
}

interface UseUserGiftsReturn {
  gifts: UserStarGift[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  totalCount: number;
}

export const useUserGifts = (
  options: UseUserGiftsOptions = {}
): UseUserGiftsReturn => {
  const { limit = 20, offset: initialOffset = "" } = options;

  const [gifts, setGifts] = useState<UserStarGift[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [nextOffset, setNextOffset] = useState<string | undefined>(
    initialOffset
  );
  const [totalCount, setTotalCount] = useState(0);

  const initDataUser = useSignal(initData.user);
  const initDataRaw = useSignal(initData.raw);

  const fetchUserGifts = useCallback(
    async (offset: string = "", append: boolean = false) => {
      if (!initDataUser || !initDataRaw) {
        setError("Telegram user data not available");
        return;
      }

      setLoading(true);
      setError(null);

      try {
        // Call our Next.js API route to fetch user gifts
        const response = await fetch("/api/user-gifts", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            user_id: initDataUser.id,
            offset,
            limit,
            initData: initDataRaw,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error ?? `HTTP error! status: ${response.status}`
          );
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error ?? "Failed to fetch user gifts");
        }

        const apiResponse: UserGiftsResponse = data.data;

        // Transform the API response to match our component's expected format
        const transformedGifts: UserStarGift[] = apiResponse.gifts.map(
          (gift) => ({
            id: `gift_${gift.gift.id}_${gift.date}`,
            name: getGiftName(gift.gift),
            sticker: gift.gift.sticker,
            stars: gift.gift.stars,
            date: gift.date * 1000, // Convert to milliseconds
            from_id: gift.from_id,
            message: gift.message?.text,
            name_hidden: gift.name_hidden,
            unsaved: gift.unsaved,
            convert_stars: gift.convert_stars ?? gift.gift.convert_stars,
          })
        );

        if (append) {
          setGifts((prev) => [...prev, ...transformedGifts]);
        } else {
          setGifts(transformedGifts);
        }

        setTotalCount(apiResponse.count);
        setNextOffset(apiResponse.next_offset);
        setHasMore(!!apiResponse.next_offset);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to fetch user gifts";
        setError(errorMessage);
        console.error("Error fetching user gifts:", err);
      } finally {
        setLoading(false);
      }
    },
    [initDataUser, initDataRaw, limit]
  );

  // Helper function to generate gift names based on emoji or gift properties
  const getGiftName = (gift: any): string => {
    if (gift.sticker?.emoji) {
      const emojiToName: { [key: string]: string } = {
        "⭐": "Golden Star",
        "💎": "Diamond Gift",
        "💖": "Crystal Heart",
        "🎂": "Birthday Gift",
        "🎁": "Mystery Gift",
        "🌟": "Shining Star",
        "💝": "Special Present",
        "🏆": "Trophy Gift",
        "👑": "Royal Gift",
        "🎉": "Celebration Gift",
      };
      return emojiToName[gift.sticker.emoji] || `${gift.sticker.emoji} Gift`;
    }

    if (gift.birthday) return "Birthday Gift";
    if (gift.limited) return "Limited Edition Gift";
    if (gift.stars >= 500) return "Premium Gift";
    if (gift.stars >= 100) return "Special Gift";

    return "Star Gift";
  };

  const loadMore = useCallback(async () => {
    if (!hasMore || loading || !nextOffset) return;
    await fetchUserGifts(nextOffset, true);
  }, [hasMore, loading, nextOffset, fetchUserGifts]);

  const refresh = useCallback(async () => {
    setGifts([]);
    setNextOffset(initialOffset);
    setHasMore(true);
    await fetchUserGifts(initialOffset, false);
  }, [fetchUserGifts, initialOffset]);

  useEffect(() => {
    if (initDataUser && initDataRaw) {
      fetchUserGifts();
    }
  }, [initDataUser, initDataRaw, fetchUserGifts]);

  return {
    gifts,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    totalCount,
  };
};
